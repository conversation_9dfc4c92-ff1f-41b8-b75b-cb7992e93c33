#!/usr/bin/env python3
"""
Test specific formatting for True/False and Match the Following questions.
"""

from utils.pdf_generator import PDFGenerator
from config import QUESTION_TYPES

def test_true_false_formatting():
    """Test True/False question formatting."""
    
    print("Testing True/False formatting...")
    
    pdf_gen = PDFGenerator()
    
    # Test True/False answer
    tf_answer = "True\nFalse ✓"
    formatted = pdf_gen._format_answer_by_type(
        QUESTION_TYPES.TRUE_FALSE,
        tf_answer,
        []
    )
    
    print(f"True/False Input: {repr(tf_answer)}")
    print(f"True/False Output: {formatted}")
    
    # Check if it contains proper formatting
    if "<br/>" in formatted and "True" in formatted and "False ✓" in formatted:
        print("✅ True/False formatting: PASSED")
        return True
    else:
        print("❌ True/False formatting: FAILED")
        return False

def test_match_following_formatting():
    """Test Match the Following question formatting."""
    
    print("\nTesting Match the Following formatting...")
    
    pdf_gen = PDFGenerator()
    
    # Test Match the Following answer
    match_answer = "1. Dil <PERSON> → Farhan Akhtar\n2. Lagaan → Ashutosh Gowariker\n3. Veer-Zaara → Yash Chopra\n4. Chak De! India → Shimit Amin\n5. Black Friday → Anurag Kashyap"
    formatted = pdf_gen._format_answer_by_type(
        QUESTION_TYPES.MATCH_FOLLOWING,
        match_answer,
        []
    )
    
    print(f"Match Input: {repr(match_answer)}")
    print(f"Match Output: {formatted}")
    
    # Check if it contains proper formatting
    if "<br/>" in formatted and "→" in formatted and "1. Dil Chahta Hai" in formatted:
        print("✅ Match the Following formatting: PASSED")
        return True
    else:
        print("❌ Match the Following formatting: FAILED")
        return False

def test_comprehensive_pdf():
    """Test comprehensive PDF generation with all question types."""
    
    print("\nTesting comprehensive PDF generation...")
    
    test_questions = [
        {
            "question_text": "Which movie features the song 'Jai Ho'?",
            "question_type": QUESTION_TYPES.MULTIPLE_CHOICE_SINGLE,
            "options": ["Om Shanti Om", "Slumdog Millionaire", "Rab Ne Bana Di Jodi", "3 Idiots"],
            "answer": "A. Om Shanti Om\nB. Slumdog Millionaire ✓\nC. Rab Ne Bana Di Jodi\nD. 3 Idiots",
            "metadata": {"page_number": 1},
            "context_used": 5
        },
        {
            "question_text": "'Wake Up Sid' is a love story between a college student and an older woman.",
            "question_type": QUESTION_TYPES.TRUE_FALSE,
            "options": [],
            "answer": "True\nFalse ✓",
            "metadata": {"page_number": 2},
            "context_used": 4
        },
        {
            "question_text": "Match the movie with its director:",
            "question_type": QUESTION_TYPES.MATCH_FOLLOWING,
            "options": [],
            "answer": "1. Dil Chahta Hai → Farhan Akhtar\n2. Lagaan → Ashutosh Gowariker\n3. Veer-Zaara → Yash Chopra\n4. Chak De! India → Shimit Amin\n5. Black Friday → Anurag Kashyap",
            "metadata": {"page_number": 3},
            "context_used": 5
        }
    ]
    
    test_json = {
        "total_questions": len(test_questions),
        "extraction_timestamp": 1234567890,
        "rag_processing_complete": True,
        "questions": test_questions
    }
    
    pdf_gen = PDFGenerator()
    output_path = "comprehensive_test.pdf"
    
    try:
        success = pdf_gen.generate_answer_pdf(test_json, output_path)
        if success:
            print(f"✅ Comprehensive PDF generated: {output_path}")
            
            # Check file size
            from pathlib import Path
            if Path(output_path).exists():
                size = Path(output_path).stat().st_size
                print(f"✅ PDF file size: {size} bytes")
                return True
            else:
                print("❌ PDF file not found")
                return False
        else:
            print("❌ Failed to generate comprehensive PDF")
            return False
            
    except Exception as e:
        print(f"❌ Error generating comprehensive PDF: {str(e)}")
        return False

def main():
    """Run all specific format tests."""
    
    print("=" * 60)
    print("Testing Specific Question Format Improvements")
    print("=" * 60)
    
    # Test individual formatting
    tf_success = test_true_false_formatting()
    match_success = test_match_following_formatting()
    pdf_success = test_comprehensive_pdf()
    
    print("\n" + "=" * 60)
    print("SPECIFIC FORMAT TEST SUMMARY")
    print("=" * 60)
    
    if tf_success:
        print("✅ True/False formatting: PASSED")
    else:
        print("❌ True/False formatting: FAILED")
    
    if match_success:
        print("✅ Match the Following formatting: PASSED")
    else:
        print("❌ Match the Following formatting: FAILED")
    
    if pdf_success:
        print("✅ Comprehensive PDF generation: PASSED")
    else:
        print("❌ Comprehensive PDF generation: FAILED")
    
    if tf_success and match_success and pdf_success:
        print("\n🎉 All specific format tests passed!")
        return True
    else:
        print("\n⚠️ Some specific format tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
